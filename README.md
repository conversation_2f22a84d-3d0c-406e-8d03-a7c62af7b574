# Philippine Airlines Website

A modern, clean, and responsive website for Philippine Airlines featuring a comprehensive booking system, service information, and user-friendly interface.

## Features

### 🎨 Design & User Experience
- **Modern Design**: Clean, professional interface with Philippine Airlines branding
- **Responsive Layout**: Optimized for desktop, tablet, and mobile devices
- **Smooth Animations**: Subtle animations and transitions for enhanced user experience
- **Accessibility**: Semantic HTML structure and keyboard navigation support

### ✈️ Booking System
- **Flight Search**: Comprehensive flight booking form with validation
- **Trip Types**: Support for round-trip, one-way, and multi-city bookings
- **Date Validation**: Smart date picker with minimum date restrictions
- **Form Validation**: Real-time validation with user-friendly error messages
- **Passenger & Class Selection**: Easy selection of passengers and travel class

### 📱 Interactive Features
- **Mobile Menu**: Responsive hamburger menu for mobile devices
- **Smooth Scrolling**: Smooth navigation between sections
- **Contact Form**: Functional contact form with validation
- **Notification System**: Toast notifications for user feedback
- **Scroll Effects**: Dynamic header styling based on scroll position

### 🌟 Sections
1. **Hero Section**: Eye-catching video background with call-to-action
2. **Booking Section**: Comprehensive flight search functionality
3. **Services Section**: Highlight of airline services and amenities
4. **Destinations Section**: Popular destinations with pricing
5. **About Section**: Company information and statistics
6. **Contact Section**: Contact information and inquiry form
7. **Footer**: Links, social media, and additional information

## Technical Stack

- **HTML5**: Semantic markup with accessibility considerations
- **CSS3**: Modern styling with CSS Grid, Flexbox, and custom properties
- **Vanilla JavaScript**: Interactive functionality without external dependencies
- **Font Awesome**: Icons for enhanced visual appeal
- **Google Fonts**: Inter font family for modern typography

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript functionality
└── README.md           # Documentation
```

## Key Features Implementation

### Responsive Design
- Mobile-first approach with breakpoints at 768px
- Flexible grid layouts that adapt to screen size
- Touch-friendly interface elements
- Optimized typography scaling

### Form Validation
- Real-time validation for booking and contact forms
- Custom error messaging system
- Date range validation for travel dates
- Email format validation

### Performance Optimizations
- Efficient CSS with custom properties for theming
- Optimized JavaScript with event delegation
- Smooth animations using CSS transforms
- Lazy loading considerations for images

### Accessibility Features
- Semantic HTML structure
- Proper heading hierarchy
- Focus management for interactive elements
- Color contrast compliance
- Screen reader friendly content

## Browser Compatibility

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Customization

### Colors
The website uses CSS custom properties for easy theming:
```css
:root {
    --primary-color: #003087;    /* Philippine Airlines Blue */
    --secondary-color: #FFD700;  /* Gold */
    --accent-color: #E31837;     /* Red */
}
```

### Content
- Update destination information in the HTML
- Modify service offerings in the services section
- Customize contact information in the contact section

## Future Enhancements

- Integration with real flight booking API
- User account system and login functionality
- Flight status tracking
- Loyalty program integration
- Multi-language support
- Advanced search filters
- Payment gateway integration

## Usage

1. Open `index.html` in a web browser
2. Navigate through sections using the menu or scroll
3. Test the booking form with sample data
4. Try the contact form functionality
5. Test responsive design by resizing the browser window

## Development Notes

- All JavaScript functionality is contained in `script.js`
- CSS is organized by component sections
- Form validation provides immediate feedback
- Smooth scrolling enhances navigation experience
- Mobile menu provides full functionality on small screens

## Credits

- Design inspired by modern airline websites
- Images from Unsplash (placeholder images)
- Icons from Font Awesome
- Fonts from Google Fonts (Inter)

---

**Note**: This is a demonstration website. For production use, integrate with actual booking systems and APIs.
