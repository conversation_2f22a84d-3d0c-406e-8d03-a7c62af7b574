// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initMobileMenu();
    initSmoothScrolling();
    initBookingForm();
    initScrollEffects();
    initContactForm();
    initAnimations();
});

// Mobile Menu Functionality
function initMobileMenu() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    navToggle.addEventListener('click', function() {
        navToggle.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
        }
    });
}

// Smooth Scrolling
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Booking Form Functionality
function initBookingForm() {
    const bookingForm = document.getElementById('bookingForm');
    const tripTypeRadios = document.querySelectorAll('input[name="tripType"]');
    const returnGroup = document.getElementById('returnGroup');
    const returnInput = document.getElementById('return');

    // Handle trip type changes
    tripTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'oneway' || this.value === 'multicity') {
                returnGroup.style.display = 'none';
                returnInput.removeAttribute('required');
            } else {
                returnGroup.style.display = 'block';
                returnInput.setAttribute('required', '');
            }
        });
    });

    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('departure').setAttribute('min', today);
    document.getElementById('return').setAttribute('min', today);

    // Handle departure date change
    document.getElementById('departure').addEventListener('change', function() {
        const departureDate = this.value;
        document.getElementById('return').setAttribute('min', departureDate);
    });

    // Form submission
    bookingForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const searchData = {
            tripType: formData.get('tripType'),
            from: formData.get('from'),
            to: formData.get('to'),
            departure: formData.get('departure'),
            return: formData.get('return'),
            passengers: formData.get('passengers'),
            class: formData.get('class')
        };

        // Validate form
        if (validateBookingForm(searchData)) {
            showSearchResults(searchData);
        }
    });
}

// Form Validation
function validateBookingForm(data) {
    const errors = [];

    if (!data.from.trim()) {
        errors.push('Please enter departure city');
    }

    if (!data.to.trim()) {
        errors.push('Please enter destination city');
    }

    if (data.from.toLowerCase() === data.to.toLowerCase()) {
        errors.push('Departure and destination cities cannot be the same');
    }

    if (!data.departure) {
        errors.push('Please select departure date');
    }

    if (data.tripType === 'roundtrip' && !data.return) {
        errors.push('Please select return date');
    }

    if (data.tripType === 'roundtrip' && data.return && new Date(data.return) <= new Date(data.departure)) {
        errors.push('Return date must be after departure date');
    }

    if (errors.length > 0) {
        showNotification(errors.join('\n'), 'error');
        return false;
    }

    return true;
}

// Show Search Results (Mock)
function showSearchResults(data) {
    showNotification('Searching for flights...', 'info');
    
    // Simulate API call
    setTimeout(() => {
        const message = `Flight search completed!\n\nFrom: ${data.from}\nTo: ${data.to}\nDeparture: ${data.departure}\n${data.return ? `Return: ${data.return}\n` : ''}Passengers: ${data.passengers}\nClass: ${data.class}`;
        showNotification(message, 'success');
    }, 2000);
}

// Contact Form
function initContactForm() {
    const contactForm = document.querySelector('.contact-form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const name = formData.get('name') || this.querySelector('input[type="text"]').value;
            const email = formData.get('email') || this.querySelector('input[type="email"]').value;
            const message = formData.get('message') || this.querySelector('textarea').value;

            if (validateContactForm(name, email, message)) {
                showNotification('Thank you for your message! We will get back to you soon.', 'success');
                this.reset();
            }
        });
    }
}

// Contact Form Validation
function validateContactForm(name, email, message) {
    const errors = [];

    if (!name.trim()) {
        errors.push('Please enter your name');
    }

    if (!email.trim()) {
        errors.push('Please enter your email');
    } else if (!isValidEmail(email)) {
        errors.push('Please enter a valid email address');
    }

    if (!message.trim()) {
        errors.push('Please enter your message');
    }

    if (errors.length > 0) {
        showNotification(errors.join('\n'), 'error');
        return false;
    }

    return true;
}

// Email Validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Notification System
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message.replace(/\n/g, '<br>')}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'error' ? '#e74c3c' : type === 'success' ? '#27ae60' : '#3498db'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 400px;
        animation: slideInRight 0.3s ease-out;
    `;

    // Add animation keyframes
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .notification-content {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                gap: 1rem;
            }
            .notification-close {
                background: none;
                border: none;
                color: white;
                font-size: 1.5rem;
                cursor: pointer;
                padding: 0;
                line-height: 1;
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Scroll Effects
function initScrollEffects() {
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        }
    });
}

// Animations on Scroll
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .destination-card, .contact-item');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Popular destinations data (for future enhancement)
const popularDestinations = [
    { city: 'Manila', country: 'Philippines', price: 299, image: 'manila.jpg' },
    { city: 'Cebu', country: 'Philippines', price: 199, image: 'cebu.jpg' },
    { city: 'Tokyo', country: 'Japan', price: 599, image: 'tokyo.jpg' },
    { city: 'Los Angeles', country: 'USA', price: 899, image: 'la.jpg' },
    { city: 'Sydney', country: 'Australia', price: 799, image: 'sydney.jpg' },
    { city: 'Singapore', country: 'Singapore', price: 399, image: 'singapore.jpg' }
];

// Airport codes for autocomplete (for future enhancement)
const airportCodes = {
    'Manila': 'MNL',
    'Cebu': 'CEB',
    'Davao': 'DVO',
    'Tokyo': 'NRT',
    'Los Angeles': 'LAX',
    'New York': 'JFK',
    'London': 'LHR',
    'Sydney': 'SYD',
    'Singapore': 'SIN',
    'Hong Kong': 'HKG'
};
